import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from './ui/dropdown-menu';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from './ui/alert-dialog';
import {
    MoreVertical,
    LogOut,
    MessageSquare,
    UserCheck,
    UserX,
    Coffee,
    Phone,
    Clock,
    Headphones,
    PhoneCall,
    History
} from 'lucide-react';

const AgentManagementGrid = ({
    agents,
    loading,
    onLogoutAgent,
    onWhisperAgent,
    onListenToCall,
    onBargeIntoCall,
    onForceStatus
}) => {
    const [logoutDialog, setLogoutDialog] = useState({ open: false, agent: null });

    const getStatusColor = (status) => {
        switch (status) {
            case 'READY':
                return 'bg-green-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-blue-500';
            case 'BREAK':
                return 'bg-purple-500';
            case 'OFFLINE':
                return 'bg-gray-400';
            default:
                return 'bg-gray-300';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'READY':
                return 'Ready';
            case 'NOT_READY':
                return 'Not Ready';
            case 'ON_CALL':
                return 'On Call';
            case 'BREAK':
                return 'Break';
            case 'OFFLINE':
                return 'Offline';
            default:
                return 'Unknown';
        }
    };

    const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    };

    const formatTime = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).format(date);
    };

    const getInitial = (name) => {
        return name ? name.charAt(0).toUpperCase() : '?';
    };

    const handleLogoutClick = (agent) => {
        setLogoutDialog({ open: true, agent });
    };

    const confirmLogout = () => {
        if (logoutDialog.agent) {
            onLogoutAgent(logoutDialog.agent.id);
        }
        setLogoutDialog({ open: false, agent: null });
    };

    if (loading) {
        return (
            <div className="p-6 bg-white border rounded-lg">
                <h2 className="mb-4 text-xl font-semibold">Agent Management</h2>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {[...Array(6)].map((_, index) => (
                        <div key={index} className="p-4 border rounded-lg animate-pulse">
                            <div className="flex items-center mb-3 space-x-3">
                                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                                <div className="flex-1">
                                    <div className="w-3/4 h-4 mb-2 bg-gray-200 rounded"></div>
                                    <div className="w-1/2 h-3 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <div className="h-3 bg-gray-200 rounded"></div>
                                <div className="w-2/3 h-3 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="p-6 bg-white border rounded-lg">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">Agents</h2>
                    <p className="text-sm text-gray-600">{agents.length} agents total</p>
                </div>

                <div className="overflow-y-auto max-h-96">
                    <div className="grid grid-cols-1 gap-3 md:grid-cols-3 lg:grid-cols-4">
                        {agents.map((agent) => (
                            <div key={agent.id} className="p-3 transition-shadow border rounded-lg hover:shadow-md">
                                <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center space-x-2">
                                        <Avatar className="w-8 h-8">
                                            <AvatarFallback className="text-xs bg-primary text-primary-foreground">
                                                {getInitial(agent.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <h3 className="text-sm font-medium">{agent.name}</h3>
                                            <p className="text-xs text-gray-600">Ext: {agent.extension}</p>
                                        </div>
                                    </div>

                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm" className="w-6 h-6 p-0">
                                                <MoreVertical className="w-3 h-3" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                            <DropdownMenuSeparator />
                                            {agent.status === 'ON_CALL' && (
                                                <>
                                                    <DropdownMenuItem
                                                        onClick={() => onListenToCall(agent.id)}
                                                    >
                                                        <Headphones className="w-4 h-4 mr-2" />
                                                        Listen to Call
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => onWhisperAgent(agent.id)}
                                                    >
                                                        <MessageSquare className="w-4 h-4 mr-2" />
                                                        Whisper to Agent
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => onBargeIntoCall(agent.id)}
                                                    >
                                                        <PhoneCall className="w-4 h-4 mr-2" />
                                                        Barge into Call
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                </>
                                            )}
                                            {agent.status !== 'ON_CALL' && agent.status !== 'OFFLINE' && (
                                                <>
                                                    <DropdownMenuItem
                                                        onClick={() => onWhisperAgent(agent.id)}
                                                    >
                                                        <MessageSquare className="w-4 h-4 mr-2" />
                                                        Whisper
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                </>
                                            )}
                                            <DropdownMenuItem
                                                onClick={() => onForceStatus(agent.id, 'READY')}
                                                disabled={agent.status === 'READY' || agent.status === 'OFFLINE'}
                                            >
                                                <UserCheck className="w-4 h-4 mr-2" />
                                                Force Ready
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                onClick={() => onForceStatus(agent.id, 'NOT_READY')}
                                                disabled={agent.status === 'NOT_READY' || agent.status === 'OFFLINE'}
                                            >
                                                <UserX className="w-4 h-4 mr-2" />
                                                Force Not Ready
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                onClick={() => onForceStatus(agent.id, 'BREAK')}
                                                disabled={agent.status === 'BREAK' || agent.status === 'OFFLINE'}
                                            >
                                                <Coffee className="w-4 h-4 mr-2" />
                                                Force Break
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                onClick={() => handleLogoutClick(agent)}
                                                disabled={agent.status === 'OFFLINE'}
                                                className="text-red-600"
                                            >
                                                <LogOut className="w-4 h-4 mr-2" />
                                                Logout Agent
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>

                                <div className="space-y-1">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-1">
                                            <div className={`w-2 h-2 rounded-full ${getStatusColor(agent.status)}`}></div>
                                            <span className="text-xs font-medium">
                                                {getStatusText(agent.status)}
                                                {agent.statusReason && ` (${agent.statusReason})`}
                                            </span>
                                        </div>
                                        {agent.statusDuration > 0 && (
                                            <span className="text-xs text-gray-500">
                                                {formatDuration(agent.statusDuration)}
                                            </span>
                                        )}
                                    </div>

                                    <div className="text-xs text-gray-600">
                                        <div className="flex items-center justify-between">
                                            <span>Queue: {agent.queue}</span>
                                            <span className="flex items-center">
                                                <Phone className="w-3 h-3 mr-1" />
                                                {agent.callsToday}
                                            </span>
                                        </div>
                                        <div className="flex items-center justify-between mt-1">
                                            <span>Last: {formatTime(agent.lastActivity)}</span>
                                            <span className="flex items-center">
                                                <Clock className="w-3 h-3 mr-1" />
                                                {Math.floor(agent.avgCallTime / 60)}:{(agent.avgCallTime % 60).toString().padStart(2, '0')}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Call History */}
                                {agent.callHistory && agent.callHistory.length > 0 && (
                                    <div className="mt-2 pt-2 border-t">
                                        <div className="flex items-center space-x-1 mb-1">
                                            <History className="h-3 w-3 text-gray-500" />
                                            <span className="text-xs font-medium text-gray-600">Recent Calls</span>
                                        </div>
                                        <div className="space-y-1 max-h-20 overflow-y-auto">
                                            {agent.callHistory.slice(0, 5).map((call) => (
                                                <div key={call.id} className="text-xs text-gray-600 flex justify-between">
                                                    <span className="truncate flex-1 mr-2">
                                                        {call.customerName}
                                                    </span>
                                                    <span className="text-xs text-gray-500">
                                                        {Math.floor(call.duration / 60)}:{(call.duration % 60).toString().padStart(2, '0')}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Current Call Info */}
                                {agent.currentCall && (
                                    <div className="mt-2 pt-2 border-t bg-blue-50 -mx-3 -mb-3 px-3 pb-3 rounded-b-lg">
                                        <div className="flex items-center space-x-1 mb-1">
                                            <Phone className="h-3 w-3 text-blue-600" />
                                            <span className="text-xs font-medium text-blue-600">Current Call</span>
                                        </div>
                                        <div className="text-xs text-blue-700">
                                            <div className="font-medium">{agent.currentCall.customerName}</div>
                                            <div className="text-blue-600">{agent.currentCall.number}</div>
                                            <div className="text-blue-500">
                                                {formatDuration(Math.floor((new Date() - new Date(agent.currentCall.startTime)) / 1000))}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <AlertDialog open={logoutDialog.open} onOpenChange={(open) => setLogoutDialog({ open, agent: null })}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Logout Agent</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to logout {logoutDialog.agent?.name}?
                            This will disconnect them from the system and end any active calls.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmLogout} className="bg-red-600 hover:bg-red-700">
                            Logout Agent
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default AgentManagementGrid;
