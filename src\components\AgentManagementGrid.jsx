import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from './ui/dropdown-menu';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from './ui/alert-dialog';
import { 
    MoreVertical, 
    LogOut, 
    MessageSquare, 
    UserCheck, 
    UserX, 
    Coffee,
    Phone,
    Clock
} from 'lucide-react';

const AgentManagementGrid = ({ 
    agents, 
    loading, 
    onLogoutAgent, 
    onWhisperAgent, 
    onForceStatus 
}) => {
    const [logoutDialog, setLogoutDialog] = useState({ open: false, agent: null });

    const getStatusColor = (status) => {
        switch (status) {
            case 'READY':
                return 'bg-green-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-blue-500';
            case 'BREAK':
                return 'bg-purple-500';
            case 'OFFLINE':
                return 'bg-gray-400';
            default:
                return 'bg-gray-300';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'READY':
                return 'Ready';
            case 'NOT_READY':
                return 'Not Ready';
            case 'ON_CALL':
                return 'On Call';
            case 'BREAK':
                return 'Break';
            case 'OFFLINE':
                return 'Offline';
            default:
                return 'Unknown';
        }
    };

    const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    };

    const formatTime = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).format(date);
    };

    const getInitial = (name) => {
        return name ? name.charAt(0).toUpperCase() : '?';
    };

    const handleLogoutClick = (agent) => {
        setLogoutDialog({ open: true, agent });
    };

    const confirmLogout = () => {
        if (logoutDialog.agent) {
            onLogoutAgent(logoutDialog.agent.id);
        }
        setLogoutDialog({ open: false, agent: null });
    };

    if (loading) {
        return (
            <div className="bg-white rounded-lg border p-6">
                <h2 className="text-xl font-semibold mb-4">Agent Management</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(6)].map((_, index) => (
                        <div key={index} className="border rounded-lg p-4 animate-pulse">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                                <div className="flex-1">
                                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <div className="h-3 bg-gray-200 rounded"></div>
                                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">Agent Management</h2>
                    <p className="text-sm text-gray-600">{agents.length} agents total</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {agents.map((agent) => (
                        <div key={agent.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-3">
                                    <Avatar className="h-10 w-10">
                                        <AvatarFallback className="bg-primary text-primary-foreground">
                                            {getInitial(agent.name)}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <h3 className="font-medium">{agent.name}</h3>
                                        <p className="text-sm text-gray-600">Ext: {agent.extension}</p>
                                    </div>
                                </div>
                                
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                            <MoreVertical className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem 
                                            onClick={() => onWhisperAgent(agent.id)}
                                            disabled={agent.status === 'OFFLINE'}
                                        >
                                            <MessageSquare className="mr-2 h-4 w-4" />
                                            Whisper
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem 
                                            onClick={() => onForceStatus(agent.id, 'READY')}
                                            disabled={agent.status === 'READY' || agent.status === 'OFFLINE'}
                                        >
                                            <UserCheck className="mr-2 h-4 w-4" />
                                            Force Ready
                                        </DropdownMenuItem>
                                        <DropdownMenuItem 
                                            onClick={() => onForceStatus(agent.id, 'NOT_READY')}
                                            disabled={agent.status === 'NOT_READY' || agent.status === 'OFFLINE'}
                                        >
                                            <UserX className="mr-2 h-4 w-4" />
                                            Force Not Ready
                                        </DropdownMenuItem>
                                        <DropdownMenuItem 
                                            onClick={() => onForceStatus(agent.id, 'BREAK')}
                                            disabled={agent.status === 'BREAK' || agent.status === 'OFFLINE'}
                                        >
                                            <Coffee className="mr-2 h-4 w-4" />
                                            Force Break
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem 
                                            onClick={() => handleLogoutClick(agent)}
                                            disabled={agent.status === 'OFFLINE'}
                                            className="text-red-600"
                                        >
                                            <LogOut className="mr-2 h-4 w-4" />
                                            Logout Agent
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`}></div>
                                        <span className="text-sm font-medium">
                                            {getStatusText(agent.status)}
                                            {agent.statusReason && ` (${agent.statusReason})`}
                                        </span>
                                    </div>
                                    {agent.statusDuration > 0 && (
                                        <span className="text-xs text-gray-500">
                                            {formatDuration(agent.statusDuration)}
                                        </span>
                                    )}
                                </div>

                                <div className="text-sm text-gray-600">
                                    <div className="flex items-center justify-between">
                                        <span>Queue: {agent.queue}</span>
                                        <span className="flex items-center">
                                            <Phone className="h-3 w-3 mr-1" />
                                            {agent.callsToday}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between mt-1">
                                        <span>Last Activity: {formatTime(agent.lastActivity)}</span>
                                        <span className="flex items-center">
                                            <Clock className="h-3 w-3 mr-1" />
                                            {Math.floor(agent.avgCallTime / 60)}:{(agent.avgCallTime % 60).toString().padStart(2, '0')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <AlertDialog open={logoutDialog.open} onOpenChange={(open) => setLogoutDialog({ open, agent: null })}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Logout Agent</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to logout {logoutDialog.agent?.name}? 
                            This will disconnect them from the system and end any active calls.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmLogout} className="bg-red-600 hover:bg-red-700">
                            Logout Agent
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default AgentManagementGrid;
