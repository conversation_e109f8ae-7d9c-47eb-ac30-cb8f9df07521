import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Button } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from './ui/tooltip';
import {
    LogOut,
    Users
} from 'lucide-react';
const Sidebar = () => {
    const { currentUser, logout } = useAuth();
    const navigate = useNavigate();

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    const getInitial = () => {
        if (!currentUser) return '?';

        const name = currentUser.name || currentUser.username || '';
        return name.charAt(0).toUpperCase();
    };

    const getStatusColor = () => {
        // For supervisor, we'll use a default color
        return 'ring-blue-500';
    };

    return (
        <div className="flex flex-col w-16 h-full overflow-hidden bg-white border-r">
            <div className="flex items-center justify-center h-16 bg-white border-b">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Avatar className={`h-10 w-10 ring-2 ${getStatusColor()}`}>
                                <AvatarFallback className="font-semibold text-white bg-blue-600">
                                    {getInitial()}
                                </AvatarFallback>
                            </Avatar>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                            <p>Supervisor</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>

            <nav className="flex-1 p-2">
                <ul className="mt-6 space-y-6">
                    {/* <li className="flex justify-center">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        className="flex items-center justify-center w-12 h-12 p-0 rounded-full"
                                        onClick={() => navigate('/supervisor')}
                                    >
                                        <Users className="w-6 h-6" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent side="right">
                                    <p>Supervisor Dashboard</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </li> */}
                </ul>
            </nav>

            <div className="flex justify-center p-4 mt-auto border-t">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="ghost"
                                className="flex items-center justify-center w-12 h-12 p-0 text-red-500 rounded-full hover:text-red-600 hover:bg-red-50"
                                onClick={handleLogout}
                            >
                                <LogOut className="w-6 h-6" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                            <p>Logout</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </div>
    );
};

export default Sidebar;
