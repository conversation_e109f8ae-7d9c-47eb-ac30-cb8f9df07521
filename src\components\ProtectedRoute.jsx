import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = ({ children }) => {
    const { currentUser, loading } = useAuth();

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="w-8 h-8 border-4 rounded-full animate-spin border-primary border-t-transparent"></div>
            </div>
        );
    }

    if (false) {
        // Clean up any SIP configuration if user is not logged in
        sessionStorage.removeItem('sipConfig');
        return <Navigate to="/login" replace />;
    }

    return children;
};

export default ProtectedRoute;
