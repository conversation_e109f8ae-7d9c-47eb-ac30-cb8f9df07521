import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { Button } from './ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from './ui/select';
import { LogOut, Users } from 'lucide-react';

const SupervisorHeader = ({ selectedQueue, onQueueChange }) => {
    const { currentUser, logout } = useAuth();

    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    // Mock queue data - in real app this would come from API
    const queues = [
        { id: 'all', name: 'All Queues' },
        { id: 'sales', name: 'Sales' },
        { id: 'support', name: 'Support' },
        { id: 'billing', name: 'Billing' },
        { id: 'technical', name: 'Technical' }
    ];

    return (
        <div className="bg-white border-b h-16 flex items-center justify-between px-6 shadow-sm">
            <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                    <div className="bg-blue-600 p-2 rounded-lg">
                        <Users className="h-5 w-5 text-white" />
                    </div>
                    <h1 className="text-xl font-bold text-gray-900">Supervisor Dashboard</h1>
                </div>
            </div>

            <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Queue:</span>
                    <Select value={selectedQueue} onValueChange={onQueueChange}>
                        <SelectTrigger className="w-40">
                            <SelectValue placeholder="Select queue" />
                        </SelectTrigger>
                        <SelectContent>
                            {queues.map((queue) => (
                                <SelectItem key={queue.id} value={queue.id}>
                                    {queue.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {currentUser && (
                    <div className="flex items-center space-x-3">
                        <div className="text-sm text-gray-600">
                            <span className="font-medium">
                                {currentUser.name || currentUser.username}
                            </span>
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleLogout}
                            className="flex items-center"
                        >
                            <LogOut className="w-4 h-4 mr-2" />
                            Logout
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default SupervisorHeader;
