import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from './ui/alert-dialog';
import { 
    LogOut, 
    MessageSquare, 
    UserCheck, 
    Coffee,
    Phone,
    Clock,
    Headphones,
    PhoneCall,
    Star,
    TrendingUp,
    Users,
    Calendar,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Pause,
    Play,
    BarChart3,
    Send,
    Eye,
    Volume2
} from 'lucide-react';

const ModernAgentGrid = ({ 
    agents, 
    loading, 
    onLogoutAgent, 
    onWhisperAgent,
    onListenToCall,
    onBargeIntoCall,
    onForceStatus 
}) => {
    const [logoutDialog, setLogoutDialog] = useState({ open: false, agent: null });

    const getStatusColor = (status) => {
        switch (status) {
            case 'READY':
                return 'bg-blue-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-green-600';
            case 'BREAK':
                return 'bg-orange-500';
            case 'OFFLINE':
                return 'bg-gray-400';
            default:
                return 'bg-gray-300';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'READY':
                return 'Ready';
            case 'NOT_READY':
                return 'Not Ready';
            case 'ON_CALL':
                return 'On Call';
            case 'BREAK':
                return 'Break';
            case 'OFFLINE':
                return 'Offline';
            default:
                return 'Unknown';
        }
    };

    const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    };

    const formatTime = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).format(date);
    };

    const getInitial = (name) => {
        return name ? name.charAt(0).toUpperCase() : '?';
    };

    const handleLogoutClick = (agent) => {
        setLogoutDialog({ open: true, agent });
    };

    const confirmLogout = () => {
        if (logoutDialog.agent) {
            onLogoutAgent(logoutDialog.agent.id);
        }
        setLogoutDialog({ open: false, agent: null });
    };

    const getPerformanceScore = (agent) => {
        // Calculate a performance score based on calls and avg time
        const efficiency = Math.max(0, 100 - (agent.avgCallTime / 300) * 100);
        const volume = Math.min(100, (agent.callsToday / 20) * 100);
        return Math.round((efficiency + volume) / 2);
    };

    if (loading) {
        return (
            <div className="bg-white rounded-xl border p-6 shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Agent Management</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {[...Array(8)].map((_, index) => (
                        <div key={index} className="border rounded-xl p-4 animate-pulse">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                                <div className="flex-1">
                                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <div className="h-3 bg-gray-200 rounded"></div>
                                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="bg-white rounded-xl border p-6 shadow-sm">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold flex items-center">
                        <Users className="w-5 h-5 mr-2 text-blue-600" />
                        Agent Management
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                            {agents.filter(a => a.status === 'READY' || a.status === 'ON_CALL').length} Active
                        </span>
                        <span>{agents.length} Total</span>
                    </div>
                </div>

                <div className="max-h-[600px] overflow-y-auto">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        {agents.map((agent) => (
                            <div 
                                key={agent.id} 
                                className={`relative group bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-sm border-l-4 transition-all duration-300 hover:shadow-lg hover:scale-[1.02] ${
                                    agent.status === 'ON_CALL' ? 'border-l-green-500' :
                                    agent.status === 'READY' ? 'border-l-blue-500' :
                                    agent.status === 'BREAK' ? 'border-l-orange-500' :
                                    agent.status === 'NOT_READY' ? 'border-l-yellow-500' :
                                    'border-l-gray-400'
                                }`}
                            >
                                <div className="p-4">
                                    {/* Agent Header */}
                                    <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center space-x-3">
                                            <div className="relative">
                                                <Avatar className="w-12 h-12 ring-2 ring-white shadow-md">
                                                    <AvatarFallback className="text-sm font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                                                        {getInitial(agent.name)}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-sm ${getStatusColor(agent.status)} ${
                                                    agent.status === 'ON_CALL' ? 'animate-pulse' : ''
                                                }`}></div>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <h3 className="text-sm font-bold text-gray-900 truncate">{agent.name}</h3>
                                                <p className="text-xs text-gray-600">Ext: {agent.extension}</p>
                                                <p className="text-xs font-medium text-blue-600">{agent.queue}</p>
                                            </div>
                                        </div>
                                        
                                        {/* Performance Score */}
                                        <div className="text-center">
                                            <div className={`text-xs font-bold ${
                                                getPerformanceScore(agent) >= 80 ? 'text-green-600' :
                                                getPerformanceScore(agent) >= 60 ? 'text-yellow-600' :
                                                'text-red-600'
                                            }`}>
                                                {getPerformanceScore(agent)}%
                                            </div>
                                            <div className="flex items-center">
                                                <Star className="w-3 h-3 text-yellow-500" />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Status and Duration */}
                                    <div className="flex items-center justify-between mb-3">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                            agent.status === 'ON_CALL' ? 'bg-green-100 text-green-800' :
                                            agent.status === 'READY' ? 'bg-blue-100 text-blue-800' :
                                            agent.status === 'BREAK' ? 'bg-orange-100 text-orange-800' :
                                            agent.status === 'NOT_READY' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {getStatusText(agent.status)}
                                            {agent.statusReason && ` (${agent.statusReason})`}
                                        </span>
                                        {agent.statusDuration > 0 && (
                                            <span className="text-xs text-gray-500 font-mono">
                                                {formatDuration(agent.statusDuration)}
                                            </span>
                                        )}
                                    </div>

                                    {/* Stats */}
                                    <div className="grid grid-cols-3 gap-2 mb-3 text-center">
                                        <div className="bg-blue-50 rounded-lg p-2">
                                            <div className="text-xs font-bold text-blue-600">{agent.callsToday}</div>
                                            <div className="text-xs text-blue-500">Calls</div>
                                        </div>
                                        <div className="bg-green-50 rounded-lg p-2">
                                            <div className="text-xs font-bold text-green-600">
                                                {Math.floor(agent.avgCallTime / 60)}:{(agent.avgCallTime % 60).toString().padStart(2, '0')}
                                            </div>
                                            <div className="text-xs text-green-500">Avg</div>
                                        </div>
                                        <div className="bg-purple-50 rounded-lg p-2">
                                            <div className="text-xs font-bold text-purple-600">
                                                {formatTime(agent.lastActivity)}
                                            </div>
                                            <div className="text-xs text-purple-500">Last</div>
                                        </div>
                                    </div>

                                    {/* Current Call Info */}
                                    {agent.currentCall && (
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-2 mb-3">
                                            <div className="flex items-center justify-between mb-1">
                                                <div className="flex items-center space-x-1">
                                                    <Phone className="h-3 w-3 text-green-600 animate-pulse" />
                                                    <span className="text-xs font-medium text-green-600">Live Call</span>
                                                </div>
                                                <span className="text-xs text-green-500 font-mono">
                                                    {formatDuration(Math.floor((new Date() - new Date(agent.currentCall.startTime)) / 1000))}
                                                </span>
                                            </div>
                                            <div className="text-xs text-green-700">
                                                <div className="font-medium truncate">{agent.currentCall.customerName}</div>
                                                <div className="text-green-600 font-mono">{agent.currentCall.number}</div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Action Buttons */}
                                    <div className="space-y-1">
                                        {agent.status === 'ON_CALL' && (
                                            <div className="grid grid-cols-3 gap-1">
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    className="h-7 text-xs"
                                                    onClick={() => onListenToCall(agent.id)}
                                                >
                                                    <Eye className="w-3 h-3" />
                                                </Button>
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    className="h-7 text-xs"
                                                    onClick={() => onWhisperAgent(agent.id)}
                                                >
                                                    <Volume2 className="w-3 h-3" />
                                                </Button>
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    className="h-7 text-xs"
                                                    onClick={() => onBargeIntoCall(agent.id)}
                                                >
                                                    <PhoneCall className="w-3 h-3" />
                                                </Button>
                                            </div>
                                        )}

                                        {agent.status !== 'ON_CALL' && agent.status !== 'OFFLINE' && (
                                            <div className="grid grid-cols-2 gap-1">
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    className="h-7 text-xs"
                                                    onClick={() => onForceStatus(agent.id, 'READY')}
                                                    disabled={agent.status === 'READY'}
                                                >
                                                    <Play className="w-3 h-3 mr-1" />
                                                    Ready
                                                </Button>
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    className="h-7 text-xs"
                                                    onClick={() => onForceStatus(agent.id, 'BREAK')}
                                                    disabled={agent.status === 'BREAK'}
                                                >
                                                    <Pause className="w-3 h-3 mr-1" />
                                                    Break
                                                </Button>
                                            </div>
                                        )}

                                        {agent.status !== 'OFFLINE' && (
                                            <Button 
                                                variant="destructive" 
                                                size="sm" 
                                                className="w-full h-7 text-xs"
                                                onClick={() => handleLogoutClick(agent)}
                                            >
                                                <LogOut className="w-3 h-3 mr-1" />
                                                Logout
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <AlertDialog open={logoutDialog.open} onOpenChange={(open) => setLogoutDialog({ open, agent: null })}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Logout Agent</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to logout {logoutDialog.agent?.name}? 
                            This will disconnect them from the system and end any active calls.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmLogout} className="bg-red-600 hover:bg-red-700">
                            Logout Agent
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default ModernAgentGrid;
