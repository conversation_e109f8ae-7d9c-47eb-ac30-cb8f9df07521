import { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from './ui/alert-dialog';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from './ui/dialog';
import {
    LogOut,
    Phone,
    Clock,
    Users,
    CheckCircle,
    Pause,
    Play,
    Eye,
    Volume2,
    History,
    PhoneCall
} from 'lucide-react';

const ModernAgentGrid = ({
    agents,
    loading,
    onLogoutAgent,
    onWhisperAgent,
    onListenToCall,
    onBargeIntoCall,
    onForceStatus
}) => {
    const [logoutDialog, setLogoutDialog] = useState({ open: false, agent: null });
    const [callHistoryDialog, setCallHistoryDialog] = useState({ open: false, agent: null });

    const getStatusColor = (status) => {
        switch (status) {
            case 'READY':
                return 'bg-blue-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-green-600';
            case 'BREAK':
                return 'bg-orange-500';
            case 'OFFLINE':
                return 'bg-gray-400';
            default:
                return 'bg-gray-300';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'READY':
                return 'Ready';
            case 'NOT_READY':
                return 'Not Ready';
            case 'ON_CALL':
                return 'On Call';
            case 'BREAK':
                return 'Break';
            case 'OFFLINE':
                return 'Offline';
            default:
                return 'Unknown';
        }
    };

    const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    };

    const formatTime = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).format(date);
    };

    const getInitial = (name) => {
        return name ? name.charAt(0).toUpperCase() : '?';
    };

    const handleLogoutClick = (agent) => {
        setLogoutDialog({ open: true, agent });
    };

    const confirmLogout = () => {
        if (logoutDialog.agent) {
            onLogoutAgent(logoutDialog.agent.id);
        }
        setLogoutDialog({ open: false, agent: null });
    };

    const handleCallHistoryClick = (agent) => {
        setCallHistoryDialog({ open: true, agent });
    };

    if (loading) {
        return (
            <div className="bg-white border p-6">
                <h2 className="text-xl font-semibold mb-4">Agent Management</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {[...Array(8)].map((_, index) => (
                        <div key={index} className="border p-4 animate-pulse">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="w-10 h-10 bg-gray-200"></div>
                                <div className="flex-1">
                                    <div className="h-4 bg-gray-200 w-3/4 mb-2"></div>
                                    <div className="h-3 bg-gray-200 w-1/2"></div>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <div className="h-3 bg-gray-200"></div>
                                <div className="h-3 bg-gray-200 w-2/3"></div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="bg-white border p-6">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold flex items-center">
                        <Users className="w-5 h-5 mr-2" />
                        Agent Management
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            {agents.filter(a => a.status === 'READY' || a.status === 'ON_CALL').length} Active
                        </span>
                        <span>{agents.length} Total</span>
                    </div>
                </div>

                <div className="max-h-[600px] overflow-y-auto">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        {agents.map((agent) => (
                            <div
                                key={agent.id}
                                className="bg-white border p-4 hover:shadow-md transition-shadow"
                            >
                                {/* Agent Header */}
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                        <div className="relative">
                                            <Avatar className="w-10 h-10">
                                                <AvatarFallback className="text-sm font-semibold bg-gray-600 text-white">
                                                    {getInitial(agent.name)}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div className={`absolute -bottom-1 -right-1 w-3 h-3 border-2 border-white ${getStatusColor(agent.status)} ${agent.status === 'ON_CALL' ? 'animate-pulse' : ''
                                                }`}></div>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h3 className="text-sm font-bold text-gray-900 truncate">{agent.name}</h3>
                                            <p className="text-xs text-gray-600">Ext: {agent.extension}</p>
                                            <p className="text-xs text-gray-600">{agent.queue}</p>
                                        </div>
                                    </div>

                                    {/* Status Badge */}
                                    <div className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800">
                                        {getStatusText(agent.status)}
                                    </div>
                                </div>

                                {/* Stats */}
                                <div className="grid grid-cols-3 gap-2 mb-3 text-center">
                                    <div className="bg-gray-50 p-2">
                                        <div className="text-xs font-bold text-gray-600">{agent.callsToday}</div>
                                        <div className="text-xs text-gray-500">Calls</div>
                                    </div>
                                    <div className="bg-gray-50 p-2">
                                        <div className="text-xs font-bold text-gray-600">
                                            {Math.floor(agent.avgCallTime / 60)}:{(agent.avgCallTime % 60).toString().padStart(2, '0')}
                                        </div>
                                        <div className="text-xs text-gray-500">Avg</div>
                                    </div>
                                    <div className="bg-gray-50 p-2">
                                        <div className="text-xs font-bold text-gray-600">
                                            {formatTime(agent.lastActivity)}
                                        </div>
                                        <div className="text-xs text-gray-500">Last</div>
                                    </div>
                                </div>

                                {/* Current Call Info */}
                                {agent.currentCall && (
                                    <div className="bg-gray-50 border p-2 mb-3">
                                        <div className="flex items-center justify-between mb-1">
                                            <div className="flex items-center space-x-1">
                                                <Phone className="h-3 w-3 text-gray-600 animate-pulse" />
                                                <span className="text-xs font-medium text-gray-600">Live Call</span>
                                            </div>
                                            <span className="text-xs text-gray-500 font-mono">
                                                {formatDuration(Math.floor((new Date() - new Date(agent.currentCall.startTime)) / 1000))}
                                            </span>
                                        </div>
                                        <div className="text-xs text-gray-700">
                                            <div className="font-medium truncate">{agent.currentCall.customerName}</div>
                                            <div className="text-gray-600 font-mono">{agent.currentCall.number}</div>
                                        </div>
                                    </div>
                                )}

                                {/* Action Buttons */}
                                <div className="space-y-1">
                                    {agent.status === 'ON_CALL' && (
                                        <div className="grid grid-cols-3 gap-1">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => onListenToCall(agent.id)}
                                            >
                                                <Eye className="w-3 h-3" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => onWhisperAgent(agent.id)}
                                            >
                                                <Volume2 className="w-3 h-3" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => onBargeIntoCall(agent.id)}
                                            >
                                                <PhoneCall className="w-3 h-3" />
                                            </Button>
                                        </div>
                                    )}

                                    {agent.status !== 'ON_CALL' && agent.status !== 'OFFLINE' && (
                                        <div className="grid grid-cols-2 gap-1">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => onForceStatus(agent.id, 'READY')}
                                                disabled={agent.status === 'READY'}
                                            >
                                                <Play className="w-3 h-3 mr-1" />
                                                Ready
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => onForceStatus(agent.id, 'BREAK')}
                                                disabled={agent.status === 'BREAK'}
                                            >
                                                <Pause className="w-3 h-3 mr-1" />
                                                Break
                                            </Button>
                                        </div>
                                    )}

                                    {/* Call History and Logout Row */}
                                    <div className="grid grid-cols-2 gap-1">
                                        {agent.callHistory && agent.callHistory.length > 0 && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => handleCallHistoryClick(agent)}
                                            >
                                                <History className="w-3 h-3 mr-1" />
                                                History
                                            </Button>
                                        )}
                                        {agent.status !== 'OFFLINE' && (
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                className="h-7 text-xs"
                                                onClick={() => handleLogoutClick(agent)}
                                            >
                                                <LogOut className="w-3 h-3 mr-1" />
                                                Logout
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Call History Modal */}
            <Dialog open={callHistoryDialog.open} onOpenChange={(open) => setCallHistoryDialog({ open, agent: null })}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Call History - {callHistoryDialog.agent?.name}</DialogTitle>
                    </DialogHeader>
                    <div className="max-h-96 overflow-y-auto">
                        <div className="space-y-3">
                            {callHistoryDialog.agent?.callHistory?.slice(0, 10).map((call) => (
                                <div key={call.id} className="border p-3 hover:bg-gray-50">
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="font-medium text-sm">{call.customerName}</div>
                                        <div className="text-xs text-gray-500">
                                            {new Intl.DateTimeFormat('en-US', {
                                                month: 'short',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            }).format(call.time)}
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between text-xs text-gray-600">
                                        <div className="flex items-center space-x-4">
                                            <span className="flex items-center">
                                                <Phone className="h-3 w-3 mr-1" />
                                                {call.number}
                                            </span>
                                            <span className="flex items-center">
                                                <Clock className="h-3 w-3 mr-1" />
                                                {Math.floor(call.duration / 60)}:{(call.duration % 60).toString().padStart(2, '0')}
                                            </span>
                                        </div>
                                        <span className={`px-2 py-1 text-xs font-medium ${call.disposition === 'Sale' ? 'bg-green-100 text-green-800' :
                                                call.disposition === 'Resolved' ? 'bg-blue-100 text-blue-800' :
                                                    call.disposition === 'Follow-up' ? 'bg-yellow-100 text-yellow-800' :
                                                        call.disposition === 'Callback' ? 'bg-purple-100 text-purple-800' :
                                                            call.disposition === 'Escalated' ? 'bg-red-100 text-red-800' :
                                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                            {call.disposition}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            <AlertDialog open={logoutDialog.open} onOpenChange={(open) => setLogoutDialog({ open, agent: null })}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Logout Agent</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to logout {logoutDialog.agent?.name}?
                            This will disconnect them from the system and end any active calls.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmLogout} className="bg-red-600 hover:bg-red-700">
                            Logout Agent
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
};

export default ModernAgentGrid;
