import { useEffect, useState } from 'react';
import Sidebar from '../components/Sidebar';
import SupervisorHeader from '../components/SupervisorHeader';
import AgentManagementGrid from '../components/AgentManagementGrid';
import SupervisorStats from '../components/SupervisorStats';

const SupervisorPage = () => {
    const [agents, setAgents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedQueue, setSelectedQueue] = useState('all');

    // Mock data for agents - in real app this would come from API
    useEffect(() => {
        const loadAgents = async () => {
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));

                const mockAgents = [
                    {
                        id: '1',
                        name: '<PERSON>',
                        username: 'jsmith',
                        status: 'READY',
                        statusDuration: 1245, // seconds
                        queue: 'Sales',
                        extension: '1001',
                        lastActivity: new Date(Date.now() - 300000), // 5 minutes ago
                        callsToday: 12,
                        avgCallTime: 180 // seconds
                    },
                    {
                        id: '2',
                        name: '<PERSON>',
                        username: '<PERSON><PERSON><PERSON><PERSON>',
                        status: 'ON_CALL',
                        statusDuration: 320,
                        queue: 'Support',
                        extension: '1002',
                        lastActivity: new Date(),
                        callsToday: 8,
                        avgCallTime: 240
                    },
                    {
                        id: '3',
                        name: 'Mike Davis',
                        username: 'mdavis',
                        status: 'BREAK',
                        statusDuration: 600,
                        statusReason: 'Lunch',
                        queue: 'Sales',
                        extension: '1003',
                        lastActivity: new Date(Date.now() - 600000), // 10 minutes ago
                        callsToday: 15,
                        avgCallTime: 160
                    },
                    {
                        id: '4',
                        name: 'Lisa Wilson',
                        username: 'lwilson',
                        status: 'NOT_READY',
                        statusDuration: 180,
                        statusReason: 'Training',
                        queue: 'Support',
                        extension: '1004',
                        lastActivity: new Date(Date.now() - 180000), // 3 minutes ago
                        callsToday: 6,
                        avgCallTime: 200
                    },
                    {
                        id: '5',
                        name: 'Tom Brown',
                        username: 'tbrown',
                        status: 'OFFLINE',
                        statusDuration: 0,
                        queue: 'Sales',
                        extension: '1005',
                        lastActivity: new Date(Date.now() - 3600000), // 1 hour ago
                        callsToday: 0,
                        avgCallTime: 0
                    }
                ];

                setAgents(mockAgents);
                setLoading(false);
            } catch (error) {
                console.error('Failed to load agents:', error);
                setLoading(false);
            }
        };

        loadAgents();
    }, []);

    const handleLogoutAgent = async (agentId) => {
        try {
            // In real app, this would call API to logout the agent
            console.log('Logging out agent:', agentId);

            setAgents(prevAgents =>
                prevAgents.map(agent =>
                    agent.id === agentId
                        ? { ...agent, status: 'OFFLINE', statusDuration: 0 }
                        : agent
                )
            );
        } catch (error) {
            console.error('Failed to logout agent:', error);
        }
    };

    const handleWhisperAgent = async (agentId) => {
        try {
            // In real app, this would initiate whisper call
            console.log('Initiating whisper to agent:', agentId);
            // For demo, just show alert
            alert(`Whisper call initiated to agent ${agents.find(a => a.id === agentId)?.name}`);
        } catch (error) {
            console.error('Failed to whisper agent:', error);
        }
    };

    const handleForceStatus = async (agentId, newStatus) => {
        try {
            // In real app, this would call API to force agent status change
            console.log('Forcing status change for agent:', agentId, 'to:', newStatus);

            setAgents(prevAgents =>
                prevAgents.map(agent =>
                    agent.id === agentId
                        ? { ...agent, status: newStatus, statusDuration: 0 }
                        : agent
                )
            );
        } catch (error) {
            console.error('Failed to force status change:', error);
        }
    };

    // Filter agents based on selected queue
    const filteredAgents = selectedQueue === 'all'
        ? agents
        : agents.filter(agent => agent.queue.toLowerCase() === selectedQueue);

    const handleQueueChange = (queueId) => {
        setSelectedQueue(queueId);
    };

    return (
        <div className="h-screen bg-gray-50 flex flex-col md:flex-row overflow-hidden">
            <Sidebar />

            <main className="flex-1 flex flex-col h-screen overflow-hidden">
                <SupervisorHeader
                    selectedQueue={selectedQueue}
                    onQueueChange={handleQueueChange}
                />

                <div className="p-4 flex-1 overflow-auto">
                    <div className="space-y-4">
                        <SupervisorStats agents={filteredAgents} />

                        <AgentManagementGrid
                            agents={filteredAgents}
                            loading={loading}
                            onLogoutAgent={handleLogoutAgent}
                            onWhisperAgent={handleWhisperAgent}
                            onForceStatus={handleForceStatus}
                        />
                    </div>
                </div>
            </main>
        </div>
    );
};

export default SupervisorPage;
