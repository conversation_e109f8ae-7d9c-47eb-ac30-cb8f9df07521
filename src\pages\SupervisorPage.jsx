import { useEffect, useState } from 'react';
import Sidebar from '../components/Sidebar';
import SupervisorHeader from '../components/SupervisorHeader';
import AgentManagementGrid from '../components/AgentManagementGrid';
import SupervisorStats from '../components/SupervisorStats';

const SupervisorPage = () => {
    const [agents, setAgents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedQueue, setSelectedQueue] = useState('all');

    // Mock data for agents - in real app this would come from API
    useEffect(() => {
        const loadAgents = async () => {
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));

                const mockAgents = [
                    {
                        id: '1',
                        name: '<PERSON>',
                        username: 'jsmith',
                        status: 'READY',
                        statusDuration: 1245, // seconds
                        queue: 'Sales',
                        extension: '1001',
                        lastActivity: new Date(Date.now() - 300000), // 5 minutes ago
                        callsToday: 12,
                        avgCallTime: 180, // seconds
                        callHistory: [
                            { id: 1, customerName: '<PERSON>', number: '+1234567890', duration: 180, time: new Date(Date.now() - 600000), disposition: 'Sale' },
                            { id: 2, customerName: 'Bob Wilson', number: '+1234567891', duration: 240, time: new Date(Date.now() - 1200000), disposition: 'Follow-up' },
                            { id: 3, customerName: '<PERSON> <PERSON>', number: '+1234567892', duration: 120, time: new Date(Date.now() - 1800000), disposition: 'No Sale' },
                            { id: 4, customerName: 'David Brown', number: '+1234567893', duration: 300, time: new Date(Date.now() - 2400000), disposition: 'Sale' },
                            { id: 5, customerName: 'Eva Martinez', number: '+1234567894', duration: 150, time: new Date(Date.now() - 3000000), disposition: 'Callback' }
                        ]
                    },
                    {
                        id: '2',
                        name: 'Sarah Johnson',
                        username: 'sjohnson',
                        status: 'ON_CALL',
                        statusDuration: 320,
                        queue: 'Support',
                        extension: '1002',
                        lastActivity: new Date(),
                        callsToday: 8,
                        avgCallTime: 240,
                        currentCall: {
                            customerName: 'Mike Thompson',
                            number: '+1234567895',
                            startTime: new Date(Date.now() - 320000),
                            type: 'Inbound'
                        },
                        callHistory: [
                            { id: 1, customerName: 'Jane Smith', number: '+1234567896', duration: 280, time: new Date(Date.now() - 900000), disposition: 'Resolved' },
                            { id: 2, customerName: 'Tom Wilson', number: '+1234567897', duration: 190, time: new Date(Date.now() - 1500000), disposition: 'Escalated' },
                            { id: 3, customerName: 'Lisa Brown', number: '+1234567898', duration: 220, time: new Date(Date.now() - 2100000), disposition: 'Resolved' },
                            { id: 4, customerName: 'Mark Davis', number: '+1234567899', duration: 310, time: new Date(Date.now() - 2700000), disposition: 'Callback' },
                            { id: 5, customerName: 'Amy Johnson', number: '+1234567800', duration: 160, time: new Date(Date.now() - 3300000), disposition: 'Resolved' }
                        ]
                    },
                    {
                        id: '3',
                        name: 'Mike Davis',
                        username: 'mdavis',
                        status: 'BREAK',
                        statusDuration: 600,
                        statusReason: 'Lunch',
                        queue: 'Sales',
                        extension: '1003',
                        lastActivity: new Date(Date.now() - 600000), // 10 minutes ago
                        callsToday: 15,
                        avgCallTime: 160,
                        callHistory: [
                            { id: 1, customerName: 'Peter Jones', number: '+1234567801', duration: 150, time: new Date(Date.now() - 700000), disposition: 'Sale' },
                            { id: 2, customerName: 'Nancy White', number: '+1234567802', duration: 180, time: new Date(Date.now() - 1300000), disposition: 'No Sale' },
                            { id: 3, customerName: 'Steve Clark', number: '+1234567803', duration: 120, time: new Date(Date.now() - 1900000), disposition: 'Follow-up' },
                            { id: 4, customerName: 'Helen Garcia', number: '+1234567804', duration: 200, time: new Date(Date.now() - 2500000), disposition: 'Sale' },
                            { id: 5, customerName: 'Paul Miller', number: '+1234567805', duration: 140, time: new Date(Date.now() - 3100000), disposition: 'Callback' }
                        ]
                    },
                    {
                        id: '4',
                        name: 'Lisa Wilson',
                        username: 'lwilson',
                        status: 'NOT_READY',
                        statusDuration: 180,
                        statusReason: 'Training',
                        queue: 'Support',
                        extension: '1004',
                        lastActivity: new Date(Date.now() - 180000), // 3 minutes ago
                        callsToday: 6,
                        avgCallTime: 200,
                        callHistory: [
                            { id: 1, customerName: 'Rachel Green', number: '+1234567806', duration: 220, time: new Date(Date.now() - 400000), disposition: 'Resolved' },
                            { id: 2, customerName: 'Kevin Lee', number: '+1234567807', duration: 190, time: new Date(Date.now() - 1000000), disposition: 'Escalated' },
                            { id: 3, customerName: 'Monica Ross', number: '+1234567808', duration: 210, time: new Date(Date.now() - 1600000), disposition: 'Resolved' },
                            { id: 4, customerName: 'Joey Taylor', number: '+1234567809', duration: 180, time: new Date(Date.now() - 2200000), disposition: 'Callback' },
                            { id: 5, customerName: 'Phoebe King', number: '+1234567810', duration: 200, time: new Date(Date.now() - 2800000), disposition: 'Resolved' }
                        ]
                    },
                    {
                        id: '5',
                        name: 'Tom Brown',
                        username: 'tbrown',
                        status: 'OFFLINE',
                        statusDuration: 0,
                        queue: 'Sales',
                        extension: '1005',
                        lastActivity: new Date(Date.now() - 3600000), // 1 hour ago
                        callsToday: 0,
                        avgCallTime: 0,
                        callHistory: []
                    }
                ];

                setAgents(mockAgents);
                setLoading(false);
            } catch (error) {
                console.error('Failed to load agents:', error);
                setLoading(false);
            }
        };

        loadAgents();
    }, []);

    const handleLogoutAgent = async (agentId) => {
        try {
            // In real app, this would call API to logout the agent
            console.log('Logging out agent:', agentId);

            setAgents(prevAgents =>
                prevAgents.map(agent =>
                    agent.id === agentId
                        ? { ...agent, status: 'OFFLINE', statusDuration: 0 }
                        : agent
                )
            );
        } catch (error) {
            console.error('Failed to logout agent:', error);
        }
    };

    const handleWhisperAgent = async (agentId) => {
        try {
            // In real app, this would initiate whisper call
            console.log('Initiating whisper to agent:', agentId);
            const agent = agents.find(a => a.id === agentId);
            alert(`Whisper call initiated to agent ${agent?.name}. You can now speak privately to the agent.`);
        } catch (error) {
            console.error('Failed to whisper agent:', error);
        }
    };

    const handleListenToCall = async (agentId) => {
        try {
            // In real app, this would start listening to the call
            console.log('Starting to listen to call for agent:', agentId);
            const agent = agents.find(a => a.id === agentId);
            alert(`Now listening to ${agent?.name}'s call with ${agent?.currentCall?.customerName}. You can hear both parties.`);
        } catch (error) {
            console.error('Failed to listen to call:', error);
        }
    };

    const handleBargeIntoCall = async (agentId) => {
        try {
            // In real app, this would join the call as a participant
            console.log('Barging into call for agent:', agentId);
            const agent = agents.find(a => a.id === agentId);
            alert(`Barging into ${agent?.name}'s call with ${agent?.currentCall?.customerName}. All parties can now hear you.`);
        } catch (error) {
            console.error('Failed to barge into call:', error);
        }
    };

    const handleForceStatus = async (agentId, newStatus) => {
        try {
            // In real app, this would call API to force agent status change
            console.log('Forcing status change for agent:', agentId, 'to:', newStatus);

            setAgents(prevAgents =>
                prevAgents.map(agent =>
                    agent.id === agentId
                        ? { ...agent, status: newStatus, statusDuration: 0 }
                        : agent
                )
            );
        } catch (error) {
            console.error('Failed to force status change:', error);
        }
    };

    // Filter agents based on selected queue
    const filteredAgents = selectedQueue === 'all'
        ? agents
        : agents.filter(agent => agent.queue.toLowerCase() === selectedQueue);

    const handleQueueChange = (queueId) => {
        setSelectedQueue(queueId);
    };

    return (
        <div className="flex flex-col h-screen overflow-hidden bg-gray-50 md:flex-row">
            <Sidebar />

            <main className="flex flex-col flex-1 h-screen overflow-hidden">
                <SupervisorHeader
                    selectedQueue={selectedQueue}
                    onQueueChange={handleQueueChange}
                />

                <div className="flex-1 p-4 overflow-auto">
                    <div className="space-y-4">
                        <SupervisorStats agents={filteredAgents} />

                        <AgentManagementGrid
                            agents={filteredAgents}
                            loading={loading}
                            onLogoutAgent={handleLogoutAgent}
                            onWhisperAgent={handleWhisperAgent}
                            onListenToCall={handleListenToCall}
                            onBargeIntoCall={handleBargeIntoCall}
                            onForceStatus={handleForceStatus}
                        />
                    </div>
                </div>
            </main>
        </div>
    );
};

export default SupervisorPage;
