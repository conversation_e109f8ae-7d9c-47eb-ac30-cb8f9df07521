import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import LoginPage from './pages/LoginPage';
import SupervisorPage from './pages/SupervisorPage';
import ProtectedRoute from './components/ProtectedRoute';

function App() {

    return (
        <>
            <AuthProvider>
                <Routes>
                    <Route path="/login" element={<LoginPage />} />
                    <Route
                        path="/supervisor"
                        element={
                            <ProtectedRoute>
                                <SupervisorPage />
                            </ProtectedRoute>
                        }
                    />
                    <Route path="/" element={<Navigate to="/supervisor" replace />} />
                </Routes>
            </AuthProvider>
        </>
    );
}

export default App;
