import { Users, Phone, Clock, TrendingUp } from 'lucide-react';

const SupervisorStats = ({ agents }) => {
    const stats = {
        totalAgents: agents.length,
        readyAgents: agents.filter(agent => agent.status === 'READY').length,
        onCallAgents: agents.filter(agent => agent.status === 'ON_CALL').length,
        totalCallsToday: agents.reduce((sum, agent) => sum + agent.callsToday, 0),
        avgCallTime: agents.length > 0 
            ? Math.round(agents.reduce((sum, agent) => sum + agent.avgCallTime, 0) / agents.length)
            : 0
    };

    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const statCards = [
        {
            title: 'Total Agents',
            value: stats.totalAgents,
            icon: Users,
            color: 'bg-blue-500',
            textColor: 'text-blue-600'
        },
        {
            title: 'Ready Agents',
            value: stats.readyAgents,
            icon: Users,
            color: 'bg-green-500',
            textColor: 'text-green-600'
        },
        {
            title: 'On Call',
            value: stats.onCallAgents,
            icon: Phone,
            color: 'bg-orange-500',
            textColor: 'text-orange-600'
        },
        {
            title: 'Calls Today',
            value: stats.totalCallsToday,
            icon: TrendingUp,
            color: 'bg-purple-500',
            textColor: 'text-purple-600'
        },
        {
            title: 'Avg Call Time',
            value: formatTime(stats.avgCallTime),
            icon: Clock,
            color: 'bg-indigo-500',
            textColor: 'text-indigo-600'
        }
    ];

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {statCards.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                    <div key={index} className="bg-white rounded-lg border p-6 shadow-sm">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                                <p className={`text-2xl font-bold ${stat.textColor}`}>
                                    {stat.value}
                                </p>
                            </div>
                            <div className={`${stat.color} p-3 rounded-full`}>
                                <IconComponent className="h-6 w-6 text-white" />
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default SupervisorStats;
